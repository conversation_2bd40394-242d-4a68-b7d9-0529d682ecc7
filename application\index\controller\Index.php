<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use ip2region\Ip2RegionClass;
use think\Exception;
use think\Request;
use ChineseUsernameGenerator;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        $request = Request::instance();
        $header = $request->header();

        // $todayCounts = db('order')
        //     ->whereTime('create_time', 'today')  // 筛选今天的订单
        //     ->field('create_ip, COUNT(id) AS count')  // 按 IP 分组并计算每组的订单数
        //     ->where('count','>',5)
        //     ->group('create_ip')  // 按 IP 分组
        //     ->select();  // 获取结果
        // halt($todayCounts);
        //        $usercenter = config('fastadmin.usercenter');
        //        $this->assign('usercenter', $usercenter);
        //        Log::write('测试日志信息，这是警告级别，并且实时写入','notice');
        //        trace('测试日志信息，这是警告级别，并且实时写入','debug');
        //        return $this->view->fetch();

        // 生成示例
        // for ($i = 0; $i < 6; $i++) {
        //     echo ChineseUsernameGenerator::generate() . '</br>';
        // }

        return 'Hello , ' . ChineseUsernameGenerator::generate_username();
    }

    public function getUserAgent()
    {
        $request = Request::instance();
        $header = $request->header();
        $useragent = $header['user-agent'];
        return $useragent;
    }

    public function tihuan()
    {
        $accountList = db('account')->select();
        foreach ($accountList as $account) {
            $config = json_decode($account['config'], true);
            $config['public_key'] = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA6JhX9BBzBbyyXr9kfEb+wKWZ5UY4la3EpkbyPM1jVXUojluXyOfsa/DP5RlLIiQ+2lcfa0V8jCZs/082yujtqoLFz95YmFsNk0A8KEgFTHgPuXgKleux2rCLKDn0B7aM66oz0hzbkKdXD+na36ooc0aLSZ/PCbvs8/OC7X+yJEmrCv12vN+gk5MVGX/Wy7Mq6v7kZqQb+v8b3GUzo9ntz9q5Nh8lAoX849DL3Ad3sc1/XMJ9RBa8BaBpAGZjH57ZsicPSuongjc79uqT9dLHi8lHDlSZKuGAnGP+8VJQdn5Xv9PfiLHMFr8m/iTIudoztQczqMeHGGLEpUIjlasXBQIDAQAB";
            $config['private_key'] = "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDomFf0EHMFvLJev2R8Rv7ApZnlRjiVrcSmRvI8zWNVdSiOW5fI5+xr8M/lGUsiJD7aVx9rRXyMJmz/TzbK6O2qgsXP3liYWw2TQDwoSAVMeA+5eAqV67HasIsoOfQHtozrqjPSHNuQp1cP6drfqihzRotJn88Ju+zz84Ltf7IkSasK/Xa836CTkxUZf9bLsyrq/uRmpBv6/xvcZTOj2e3P2rk2HyUChfzj0MvcB3exzX9cwn1EFrwFoGkAZmMfntmyJw9K6ieCNzv26pP10seLyUcOVJkq4YCcY/7xUlB2fle/09+IscwWvyb+JMi52jO1BzOox4cYYsSlQiOVqxcFAgMBAAECggEACeUROMzKV+AjJmy19aN/iuqAlYRbO+LcjsZN39BM/YGya/vxhRZ4tE7WSUS+MYGhOuCh8YpPNhccY/AWwKMQGUTe7WzkD/cvqKf+5yfJMeuZn6Y/rQkN0+PWx6tPelSFwjDHa5TWslysE307VnCCsAS8QDGQOTXJasddNoVMKsbywGFHq/gSSTmvH7hRLjgvwxXiXH6FbeOgt1fKY5myaHTxPkIKrMJUT+C6VfPtQJV6GfUqEVMB5c6FuZw+lQQScNwS5/Z5vxC5aizg0is+/x0MMQ6Q++0Lu0MqVZzMQN5QucWPAucd4kXT3pXqPwZuJRF8UYRRFytso/1eLSuaAQKBgQD3LDsKNS5NMKvlq8EAsRT7vFn2Z9UwYzGM517YqyrofDbYn7ArmvXpFbPVF9F1jBXwsTPQqd+VGu+B4929GZu3eAK4+Kckf38EPvcWXunNDqvcJrZtomJrzJ1SlHFnOwUdIKObbk3x0qB2Bp3atXu0fr1rtnwduCkmd+EOXxu7oQKBgQDw5tZAtUKQmrvQLA1adlCBJ4O9qYQvIuh/oCJ6PzH1VedNYOADbnL2ljCYII9VG4y0jYHRGkzkuuer90G+hB2g2yaP+3RwUjG++1JX2KYGZICmNlTkau1MnHT9GEZ6hogkNZEog5s9rHTOrefRSEFfq9q7jN1aXT1O5ZLVznRA5QKBgCL8A69CL7eFOA9d7AdpCQtIcYNK1wXuREkpRmdscrpNOoYWtBy35i6Df9ydbmww07opmqfiI187XMMJuYzWXw/s0JXVu/KSiSvUraxg6r63YOPcJt4FqeCQKYRStmpiFCs3P3D0f9vGcCfEb/tQzJ2TgsQIJDEgq9JiDVy3YXmBAoGBANvqn2TrFB1WZGYiEP6oS83b+NRH0DgHsh/wXAklb8H77fAho97+EU0yxqHZ939GayjYlc4CKHgWeC/wxL/bdADIj2eZsi5nmqoyXHV9ffMQBLG3n7/2LTNnERWL96sa7WXA0QcfPbULnoafE6VmROfFxyS378rym2lPAsDUfCRRAoGAZ3I6YwAhYrvOWDD/rLLXiAoabHiCl0yb7YJvpfXoCwk5YZO77RuBB8d6PtDmQpXsMHolHdec5eNztO2v2F50XHxWqCFYNogbhNln7wXawXQfK7rkw1PiefjKeY3rJRwBtwNbw8/l6exV2cpN+cbzwoH7qg+k5SHS8WzkG2WQsrY=";
            db('account')->where('id', $account['id'])->update(['config' => json_encode($config)]);
        }
    }

    public function od()
    {
        //
        $id = input('id');
        $order = db('order')->where('id', $id)->find();
        echo '';
        return redirect($order['pay_url']);
    }

    public function ali()
    {
        $out_trade_no = input('out_trade_no');
        $order = db('order')->where('out_trade_no', $out_trade_no)->find();
        return redirect($order['pay_url']);
    }
}
