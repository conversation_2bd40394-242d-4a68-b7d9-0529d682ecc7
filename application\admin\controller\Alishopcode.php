<?php

namespace app\admin\controller;

use AlipayService;
use app\admin\model\Merchants;
use app\admin\model\Order;
use app\common\controller\Backend;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;
use think\exception\ValidateException;
use fast\Http;
use GuzzleHttp\Client;

/**
 * 支付宝商家码
 *
 * @icon fa fa-circle-o
 */
class Alishopcode extends Backend
{
    protected $dataLimit = "auth";

    protected $dataLimitField = "admin_id";

    protected $noNeedRight = ['*'];

    /**
     * Account模型对象
     * @var \app\admin\model\Account
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Account;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }

            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                    ->with(['admin'])
                    ->where($where)
                    ->where('channel','9009')
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
				$row->getRelation('admin')->visible(['nickname']);
                $row['today'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->sum('amount');
                $row['today'] = number_format($row['today'],2);
                $row['yesterday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'yesterday')
                    ->sum('amount');
                $row['yesterday'] = number_format($row['yesterday'],2);
                $row['beforeday'] = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'between',[date('Y-m-d',time()-2*24*3600),date('Y-m-d',time()-1*24*3600)])
                    ->sum('amount');
                $row['beforeday'] = number_format($row['beforeday'],2);
                $ispay = Order::where('account_identity',$row['account_identity'])
                    ->where('pay_status',1)
                    ->whereTime('create_time', 'today')
                    ->count();
                $all = Order::where('account_identity',$row['account_identity'])
                    ->whereTime('create_time', 'today')
                    ->count();
                $row['today_rate'] =  $all==0 ? "0" : number_format(($ispay/$all) * 100,2);
            }
            unset($row);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }

        // 计算账号统计数据
        $adminIds = $this->getDataLimitAdminIds();

        // 在线账号数量（正常状态且无异常信息）
        $onlineQuery = $this->model->where('channel','9009')
            ->where('status', 'normal')
            ->where(function($query) {
                $query->where('statusinfo', '')->whereOr('statusinfo', null);
            });
        if (is_array($adminIds)) {
            $onlineQuery->where($this->dataLimitField, 'in', $adminIds);
        }
        $onlineCount = $onlineQuery->count();

        // 离线账号数量（禁用状态或有异常信息）
        $offlineQuery = $this->model->where('channel','9009')
            ->where(function($query) {
                $query->where('status', 'hidden')
                      ->whereOr(function($subQuery) {
                          $subQuery->where('statusinfo', '<>', '')
                                   ->where('statusinfo', 'not null');
                      });
            });
        if (is_array($adminIds)) {
            $offlineQuery->where($this->dataLimitField, 'in', $adminIds);
        }
        $offlineCount = $offlineQuery->count();

        // 传递统计数据到视图
        $this->view->assign('onlineCount', $onlineCount);
        $this->view->assign('offlineCount', $offlineCount);

        return $this->view->fetch();
    }

    /**
     * 添加
     *
     * @return string
     * @throws \think\Exception
     */
    public function add()
    {

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');

        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }

        $params = $this->preExcludeFields($params);
        if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
            $params[$this->dataLimitField] = $this->auth->id;
        }
        if ($this->model->where('name',$params['name'])->where('channel','9009')->find() != null) {
            $this->error("该账号已存在");
        }
        if ($this->model->where('account_identity',$params['account_identity'])->where('channel','9009')->find() != null) {
            $this->error("该账号已存在");
        }
        // 从params['game_url']中解析二维码信息
        $qrcode_content = $this->parseQrcode($params['game_url']);
        if (empty($qrcode_content)) {
            $this->error("二维码解析失败");
        }
        // 格式化配置信息
        $params['config'] = json_encode([
            'game_url' => $params['game_url'],
            'qrcode_content' => $qrcode_content,
        ], JSON_THROW_ON_ERROR);
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                $this->model->validateFailException()->validate($validate);
            }
            $result = $this->model->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($result === false) {
            $this->error(__('No rows were inserted'));
        }
        $this->success();
    }

    /**
     * 编辑
     *
     * @param $ids
     * @return string
     * @throws DbException
     * @throws \think\Exception
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds) && !in_array($row[$this->dataLimitField], $adminIds)) {
            $this->error(__('You have no permission'));
        }
        if (false === $this->request->isPost()) {
            // 格式化配置信息
            $row['config'] = json_decode($row['config'],true);
            $this->view->assign('row', $row);
            return $this->view->fetch();
        }
        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        // 从params['game_url']中解析二维码信息
        $qrcode_content = $this->parseQrcode($params['game_url']);
        if (empty($qrcode_content)) {
            $this->error("二维码解析失败");
        }
        // 格式化配置信息
        $params['config'] = json_encode([
            'game_url' => $params['game_url'],
            'qrcode_content' => $qrcode_content,
        ], JSON_THROW_ON_ERROR);
        unset($params['admin_id']);
        $params['statusinfo'] = '';
        $result = false;
        Db::startTrans();
        try {
            //是否采用模型验证
            if ($this->modelValidate) {
                $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                $row->validateFailException()->validate($validate);
            }
            $result = $row->allowField(true)->save($params);
            Db::commit();
        } catch (ValidateException|PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if (false === $result) {
            $this->error(__('No rows were updated'));
        }
        $this->success();
    }

    /**
     * 删除
     *
     * @param $ids
     * @return void
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post("ids");
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }
        $pk = $this->model->getPk();
        $row = $this->model->get($ids);
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $list = $this->model->where($pk, 'in', $ids)->select();

        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    public function test($ids)
    {
        if ($this->request->isPost()) {
            $ids = $ids ?: $this->request->post("ids");
            if (empty($ids)) {
                $this->error(__('Parameter %s can not be empty', 'ids'));
            }
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $row = $this->model->where($pk,$ids)->find();
            $params = $this->request->post('row/a');

            $out_trade_no = \app\common\library\Order::createUniqueNo();
            $data = [
                'out_trade_no' => $out_trade_no,
                'merchants_code' => 'MC100020_15',
                'amount' => $params['price'],
                'channel_code' => $row["channel"],
                'notify_url' => 'haha',
                'test'=>'true'
            ];
            $sign = md5(urldecode($this->ascii($data)) . "&key=1a04a03e398dde1390e699ada41d4bd1");
            $url = $this->request->domain()."/api/order/newcreate?out_trade_no=".$out_trade_no."&merchants_code=MC100020_15&amount=".$data['amount']."&channel_code=".$data['channel_code']."&notify_url=haha&test=true&sign=".$sign;
            
            $res = Http::post($url);
            // halt($res);
            $res = json_decode($res,true);

            if ($res['code'] == 0){
                $this->error($res['msg']);
            }
            $qr_url = $this->request->domain().'/api/demo/qrcode?codeText='.urlencode($res['data']);
            // $qr_url = 'https://qun.qq.com/qrcode/index?data='.urlencode($res['data']['url']);
            echo '<img src="'.$qr_url.'" alt="二维码" width=300px>';
            die;
            // return $this->redirect($res['data']['url']);
        }

        return $this->view->fetch();
    }
    
    protected function ascii($params = array()){
        unset($params['undefined']);
        unset($params['sign']);
        //ksort()对数组按照键名进行升序排序
        ksort($params);
        //reset()内部指针指向数组中的第一个元素
        reset($params);
        $str = http_build_query($params, '', '&');
        return $str;
    }
    

    /**
     * 批量更新
     *
     * @param $ids
     * @return void
     */
    public function multi($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        if (false === $this->request->has('params')) {
            $this->error(__('No rows were updated'));
        }
        parse_str($this->request->post('params'), $values);
        $values = $this->auth->isSuperAdmin() ? $values : array_intersect_key($values, array_flip(is_array($this->multiFields) ? $this->multiFields : explode(',', $this->multiFields)));

        if (empty($values)) {
            $this->error(__('You have no permission'));
        }

        if ($values['status'] == 'normal'){
            $values['statusinfo'] = '';
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save($values);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    public function paylimit($ids)
    {
        $ids = $ids ?: $this->request->post('ids');
        if (empty($ids)) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }

        if (false === $this->request->isPost()) {
            return $this->view->fetch();
        }

        $params = $this->request->post('row/a');
        if (empty($params)) {
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $params = $this->preExcludeFields($params);

        $count = 0;
        Db::startTrans();
        try {
            $list = $this->model->where($this->model->getPk(), 'in', $ids)->select();
            foreach ($list as $item) {
                $count += $item->allowField(true)->isUpdate(true)->save(["pay_limit"=>$params['pay_limit']]);
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were updated'));
    }

    /**
     * 解析二维码内容
     * @param string $imageUrl 二维码图片地址
     * @return string 解析出的内容，失败返回空字符串
     */
    private function parseQrcode($imageUrl)
    {
        try {
            // 如果是相对路径，转换为完整URL
            if (strpos($imageUrl, 'http') !== 0) {
                $imageUrl = $this->request->domain() . $imageUrl;
            }

            // 尝试多个API，按优先级顺序
            $apis = [
                // API 1: 原来使用的 uomg API
                [
                    'url' => "https://api.uomg.com/api/qr.decode?url=" . urlencode($imageUrl),
                    'parser' => function($response) {
                        $result = json_decode($response, true);
                        if (isset($result['code']) && $result['code'] == 1 && isset($result['qrurl'])) {
                            return $result['qrurl'];
                        }
                        return false;
                    }
                ],
                // API 2: QR Server API
                [
                    'url' => "https://api.qrserver.com/v1/read-qr-code/?fileurl=" . urlencode($imageUrl),
                    'parser' => function($response) {
                        $result = json_decode($response, true);
                        if (is_array($result) && isset($result[0]['symbol'][0]['data'])) {
                            return $result[0]['symbol'][0]['data'];
                        }
                        return false;
                    }
                ],
                // API 3: 备用免费API
                [
                    'url' => "http://api.qrserver.com/v1/read-qr-code/?fileurl=" . urlencode($imageUrl),
                    'parser' => function($response) {
                        $result = json_decode($response, true);
                        if (is_array($result) && isset($result[0]['symbol'][0]['data'])) {
                            return $result[0]['symbol'][0]['data'];
                        }
                        return false;
                    }
                ]
            ];

            // 依次尝试每个API
            foreach ($apis as $index => $api) {
                try {
                    \think\Log::info("尝试API " . ($index + 1) . ": " . $api['url']);
                    $response = Http::get($api['url']);

                    if (!empty($response)) {
                        $content = $api['parser']($response);
                        if ($content !== false && !empty($content)) {
                            \think\Log::info("API " . ($index + 1) . " 解析成功: " . $content);
                            return $content;
                        }
                    }
                    \think\Log::warning("API " . ($index + 1) . " 解析失败，响应: " . substr($response, 0, 200));
                } catch (\Exception $e) {
                    \think\Log::error("API " . ($index + 1) . " 请求异常: " . $e->getMessage());
                    continue;
                }
            }

            // 所有API都失败，返回空字符串
            \think\Log::error('所有二维码解析API都失败');
            return '';

        } catch (\Exception $e) {
            // 记录错误日志
            \think\Log::error('二维码解析失败: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 获取图片的本地路径
     * @param string $imageUrl 图片URL或相对路径
     * @return string 本地文件路径
     */
    private function getLocalImagePath($imageUrl)
    {
        // 如果是相对路径，转换为绝对路径
        if (strpos($imageUrl, 'http') !== 0) {
            // 移除开头的斜杠
            $imageUrl = ltrim($imageUrl, '/');
            return ROOT_PATH . 'public' . DS . $imageUrl;
        }

        // 如果是完整URL，检查是否是本站的图片
        $domain = $this->request->domain();
        if (strpos($imageUrl, $domain) === 0) {
            $relativePath = str_replace($domain, '', $imageUrl);
            $relativePath = ltrim($relativePath, '/');
            return ROOT_PATH . 'public' . DS . $relativePath;
        }

        // 如果是外部URL，需要下载到本地
        return $this->downloadImage($imageUrl);
    }

    /**
     * 下载外部图片到本地临时目录
     * @param string $imageUrl 图片URL
     * @return string 本地文件路径
     */
    private function downloadImage($imageUrl)
    {
        try {
            $tempDir = ROOT_PATH . 'runtime' . DS . 'temp' . DS;
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            $filename = 'qrcode_' . md5($imageUrl) . '.png';
            $localPath = $tempDir . $filename;

            // 如果文件已存在，直接返回
            if (file_exists($localPath)) {
                return $localPath;
            }

            // 下载图片
            $imageData = Http::get($imageUrl);
            if ($imageData) {
                file_put_contents($localPath, $imageData);
                return $localPath;
            }

            return '';

        } catch (\Exception $e) {
            \think\Log::error('下载图片失败: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 备用二维码解析方法
     * @param string $imageUrl 二维码图片地址
     * @return string 解析出的内容，失败返回空字符串
     */
    private function parseQrcodeBackup($imageUrl)
    {
        try {
            // 尝试使用另一个API
            $apiUrl = "https://api.qrserver.com/v1/read-qr-code/?fileurl=" . urlencode($imageUrl);
            $response = Http::get($apiUrl);
            $result = json_decode($response, true);

            if (isset($result[0]['symbol'][0]['data'])) {
                return $result[0]['symbol'][0]['data'];
            }

            // 如果还是失败，尝试第三个API
            return $this->parseQrcodeThirdAPI($imageUrl);

        } catch (\Exception $e) {
            \think\Log::error('备用二维码解析失败: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 第三个备用API解析方法
     * @param string $imageUrl 二维码图片地址
     * @return string 解析出的内容，失败返回空字符串
     */
    private function parseQrcodeThirdAPI($imageUrl)
    {
        try {
            // 尝试使用第三个API - webqr.com的API
            $apiUrl = "https://api.qrcode-monkey.com/qr/custom";

            // 或者尝试其他免费的二维码解析API
            $apiUrl2 = "http://api.qrserver.com/v1/read-qr-code/?fileurl=" . urlencode($imageUrl);

            $response = Http::get($apiUrl2);
            $result = json_decode($response, true);

            if (is_array($result) && isset($result[0]['symbol'][0]['data'])) {
                return $result[0]['symbol'][0]['data'];
            }

            return '';

        } catch (\Exception $e) {
            \think\Log::error('第三个API解析失败: ' . $e->getMessage());
            return '';
        }
    }

    /**
     * 测试二维码解析功能
     * 可以通过访问 /admin/alishopcode/testqrcode?url=图片地址 来测试
     */
    public function testqrcode()
    {
        $imageUrl = $this->request->get('url');
        if (empty($imageUrl)) {
            $this->error('请提供二维码图片地址');
        }

        // 显示详细的调试信息
        $imagePath = $this->getLocalImagePath($imageUrl);
        $debugInfo = [
            'original_url' => $imageUrl,
            'local_path' => $imagePath,
            'file_exists' => file_exists($imagePath),
            'file_size' => file_exists($imagePath) ? filesize($imagePath) : 0
        ];

        $content = $this->parseQrcode($imageUrl);

        $debugInfo['parsed_content'] = $content;

        if (empty($content)) {
            $this->error('二维码解析失败', $debugInfo);
        }

        $this->success('解析成功', $debugInfo);
    }

}
