<?php
namespace app\crontab\command;

ini_set ("memory_limit","-1");

use AlipayService;
use Psr\Http\Message\ResponseInterface;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\input\Option;
use think\console\Output;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Pool;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use think\exception\DbException;
use think\Log;

class Task extends Command
{
    protected function configure()
    {
        $this->setName('Task')
            ->setDescription('task定时任务');
    }

    protected function execute(Input $input, Output $output)
    {
        while (true) {
            // 输出开始时间
//            $output->writeln("TaskCommand:start:time:".date('Y-m-d H:i:s'));
            $startTime = time();

            // 获取5分钟内的订单
            $orderList = db('order')
                ->where('pay_status', 0)
                ->where('pay_url', 'not null')
                ->whereTime('create_time', '-5 minutes')
                ->select();

//            for($i = 0; $i < 11; $i++) {
//                $orderList = array_merge($orderList,$orderList);
//            }
            $output->writeln(date('Y-m-d H:i:s')." - 获取5分钟内的订单:".count($orderList)."笔");

            try {
                $this->alipayCheck($orderList);
            } catch (Exception $e) {
                Log::write('支付宝账单查询失败，异常信息：'.$e->getMessage(),'error');
            }

            $output->writeln("TaskCommand:execute:time:".($endTime = time() - $startTime)."s");
            // 休眠5秒
            sleep(5);
        }

    }

    protected function alipayCheck($orderList){
        $client = new Client(['verify' => false]);// 实例化Guzzle

        // 定义请求
        $requests = function ($orderList) {
            foreach ($orderList as $order) {
                $start_time = datetime($order['create_time']); // 订单创建时间
                $end_time = datetime(time()); // 当前时间
                // 获取账号信息
                $account = db('account')->where('account_identity', $order['account_identity'])->find();
                // 账号信息为空 跳过
                if(empty($account)) continue;
                $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);

                $uri = 'https://openapi.alipay.com/gateway.do';
                $requestConfigs = array(
                    'start_time' => $start_time,
                    'end_time' => $end_time,
                );
                $commonConfigs = array(
                    'app_id' => $accountConfig['appid'],
                    'method' => 'alipay.data.bill.accountlog.query', //接口名称
                    'format' => 'JSON',
                    'charset'=> 'utf-8',
                    'sign_type'=> 'RSA2',
                    'timestamp'=> $start_time,
                    'version'=> '1.0',
                    'biz_content'=> json_encode($requestConfigs),
                );

                $aliPay = new AlipayService();
                $aliPay->setAppid($accountConfig['appid']);
                $aliPay->setRsaPrivateKey($accountConfig['private_key']);
                $commonConfigs["sign"] = $aliPay->generateSign($commonConfigs, $commonConfigs['sign_type']);
                $x = $aliPay->buildOrderStr($commonConfigs);
                yield new Request('GET', "{$uri}?$x");
            }
        };

        // 并发请求
        $pool = new Pool($client, $requests($orderList), [
            'concurrency' => 25,
            'fulfilled' => function (Response $response, $index) use ($orderList) {

                // 成功响应
                $result = json_decode($response->getBody()->getContents(), true, 512, JSON_THROW_ON_ERROR)['alipay_data_bill_accountlog_query_response'];
                if($result['code'] != 10000) {
                    Log::write($orderList[$index]['out_trade_no'].'支付宝账单查询失败，返回信息：'.$result['msg'],'error');
                }
                if($result['code'] == 10000){
                    Log::write('支付宝账单查询成功，返回信息：'.json_encode($result),'info');
                    if (!empty($result['detail_list'])) {
                        foreach ($result['detail_list'] as $detail) {
                            // if(!empty($detail['trans_memo'] && $detail['direction']=="收入"))
                            // $beizhu = explode('|', $detail['trans_memo']);
                            if($detail['trans_amount']==$orderList[$index]['amount'] && $detail['direction']=="收入")
                            {
                                // 查询支付宝单号是否已经存在
                                $od = db('order')->where('pay_trade_no',$detail['alipay_order_no'])->find();
                                if(empty($od)){
                                    // 更新订单支付状态
                                    db('order')
                                        ->where('id', $orderList[$index]['id'])
                                        ->update(['pay_status' => 1, 'pay_trade_no' => $detail['alipay_order_no']]);
                                    // 查询商户信息
                                    $merchants = db('merchants')->where('id', $orderList[$index]['merchants_id'])->find();
                                    if (\app\common\library\Order::merchantsCallback($orderList[$index]['callback_url'], $merchants['key'], $orderList[$index]['out_trade_no'], $orderList[$index]['amount'], $orderList[$index]['channel_code'], 1)) {
                                        // 更新订单回调状态
                                        db('order')
                                            ->where('id', $orderList[$index]['id'])
                                            ->update(['callback_status' => 1, 'callback_time' => time()]);
                                    } else {
                                        Log::write('【回调失败】 订单号：'.$orderList[$index]['out_trade_no'],'error');
                                    }
                                }
                                
                            }
                        }
                    }

                }
            },
            'rejected' => function (RequestException $reason, $index)  use ($orderList) {
                // 失败的请求
                Log::write('订单号：'.$orderList[$index]['out_trade_no'].'账号：'.$orderList[$index]['account_identity'].'发生未知异常，异常信息：'.$reason->getMessage(),'error');
            },
        ]);
        // 发起转移并创建 Promise
        $promise = $pool->promise();
        // 等待所有请求完成
        $promise->wait();
    }




//    protected function alipayCheck($orderList){
//        foreach ($orderList as $order) {
//            // 获取账号信息
//            $account = db('account')->where('account_identity', $order['account_identity'])->find();
//            // 账号信息为空 跳过
//            if(empty($account)) continue;
//            $accountConfig = json_decode($account['config'], true, 512, JSON_THROW_ON_ERROR);
//            // 支付宝配置
//            $alipay_config = [
//                'app_id' => $accountConfig['appid'],
//                /**
//                 * 支付宝公钥
//                 * （使用公钥证书模式此项可留空）
//                 */
//                'alipay_public_key' => $accountConfig['public_key'],
//                /**
//                 * 应用私钥
//                 */
//                'app_private_key' => $accountConfig['private_key'],
//                //=======【其他设置，一般无需修改】=======
//                /**
//                 * 签名方式,默认为RSA2
//                 */
//                'sign_type' => "RSA2",
//                /**
//                 * 编码格式
//                 */
//                'charset' => "UTF-8",
//                /**
//                 * 支付宝网关
//                 */
//                'gateway_url' => "https://openapi.alipay.com/gateway.do",
//            ];
//            //发起查询请求
//            try{
//                $start_time = datetime($order['create_time']); // 订单创建时间
//                $end_time = datetime(time()); // 当前时间
//                //实例化支付宝SDK
//                $aop = new \Alipay\AlipayBillService($alipay_config);
//                // 发起查询请求
//                $result = $aop->accountlogQuery($start_time, $end_time);
//                if($result['code'] !== 10000) {
//                    Log::write('订单号：'.$order['out_trade_no'].'支付宝账单查询失败，返回信息：'.$result['msg'],'error');
//                    continue;
//                }
//                if($result['code'] == 10000){
//                    if(empty($result['detail_list'])) continue; // 账单记录为空 跳过
//                    foreach ($result['detail_list'] as $detail) {
//                        if($detail['trans_memo']===$order['out_trade_no']){ // 订单号匹配
//                            // 支付成功
//                            db('order')->where('id', $order['id'])->update(['pay_status' => 1]); // 更新订单状态
//                            Log::write('订单号：'.$order['out_trade_no'].'支付宝账单查询成功，返回信息：'.$result['msg'],'info');
//                        }
//                    }
//                }
//
//            }catch(Exception $e){
//                Log::write('订单号：'.$order['out_trade_no'].'发生未知异常，异常信息：'.$e->getMessage(),'error');
//                continue;
//            }
//
//        }
//    }
}
